<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { t, language } from '$lib/stores/i18n';
	import { toastStore } from '$lib/stores/toastStore';
	import {
		Button,
		Badge,
		Dropdown,
		Checkbox,
		Label
	} from 'flowbite-svelte';
	import { get } from 'svelte/store';
	import { displayDate } from '$lib/utils';

	// Import API services
	import { services } from '$src/lib/api/features';

	// Import modal components
	import ModalPortal from '$lib/components/common/ModalPortal.svelte';
	import PolicyDetailModal from '$lib/components/customer/modals/PolicyDetailModal.svelte';
	import { AdjustmentsHorizontalSolid, ChevronDownOutline } from 'flowbite-svelte-icons';

	// Props following existing tab component patterns
	// Customer ID for context
	export let customerId: number;
	// Platform ID for context
	export let platformId: number;
	// Authentication token for API requests
	export let access_token: string;

	// Component state
	let loading = false;
	let error = '';
	let lang = get(language);

	// Two-stage loading state
	let policyListLoading = false;
	let policyDetailsLoading = false;
	let policyListData: any = null;
	let selectedPolicyDetails: any = null;

	// Enhanced error handling and workflow status
	let workflowStatus: 'idle' | 'executing' | 'success' | 'error' | 'fallback' = 'idle';
	let workflowError: string = '';
	let workflowStep: string = '';
	let isUsingFallback = false;

	// Initial data loading state management
	let initialDataLoaded = false;
	let initialDataLoading = false;

	// Track current customer ID and platform ID to reset flags when it changes
	let currentCustomerId: number | null = null;
	let currentPlatformId: number | null = null;

	// Modal states
	let policyDetailModalOpen = false;
	let selectedPolicy: any | null = null;

	// Filter state management
	let activeFilter: string | null = null;

	// Dropdown filter state management
	let selectedPolicyStatuses: Set<string> = new Set(['All']);

	// Policy status filter options
	const policyStatusOptions = [
		{ id: 'All', label: 'policy_status_all' },
		{ id: 'active', label: 'policy_status_active' },
		{ id: 'pending', label: 'policy_status_pending' },
		{ id: 'nearly_expired', label: 'policy_status_nearly_expired' },
		{ id: 'expired', label: 'policy_status_expired' }
	];

	// Count of active dropdown filters (excluding 'All')
	$: activeDropdownFiltersCount = selectedPolicyStatuses.has('All') ? 0 : selectedPolicyStatuses.size;

	// Policy status counts - reactive calculations
	$: policyStatusCounts = (() => {
		if (!policyListData?.policy_list_data?.ListOfPolicyListSocial) {
			return {
				active: 0,
				pending: 0,
				nearly_expired: 0,
				expired: 0
			};
		}

		const counts = {
			active: 0,
			pending: 0,
			nearly_expired: 0,
			expired: 0
		};

		policyListData.policy_list_data.ListOfPolicyListSocial.forEach((policy: any) => {
			const status = getDisplayStatus(policy.EffFrom, policy.EffTo);
			if (status in counts) {
				counts[status as keyof typeof counts]++;
			}
		});

		return counts;
	})();

	// Filtered policies based on active filter (prioritizes dropdown over summary cards)
	$: filteredPolicies = (() => {
		if (!policyListData?.policy_list_data?.ListOfPolicyListSocial) {
			return [];
		}

		const allPolicies = policyListData.policy_list_data.ListOfPolicyListSocial;

		// If dropdown filters are active (not 'All'), use dropdown filtering
		if (!selectedPolicyStatuses.has('All')) {
			return allPolicies.filter((policy: any) => {
				const policyStatus = getDisplayStatus(policy.EffFrom, policy.EffTo);
				return selectedPolicyStatuses.has(policyStatus);
			});
		}

		// If no dropdown filter but summary card filter is active, use summary card filtering
		if (activeFilter) {
			return allPolicies.filter((policy: any) => {
				const policyStatus = getDisplayStatus(policy.EffFrom, policy.EffTo);
				return policyStatus === activeFilter;
			});
		}

		// No filters active, return all policies
		return allPolicies;
	})();

	// Toggle policy status filter in dropdown
	function togglePolicyStatusFilter(statusId: string) {
		const newSelected = new Set(selectedPolicyStatuses);

		if (statusId === 'All') {
			// If 'All' is selected, clear all other selections
			newSelected.clear();
			newSelected.add('All');
		} else {
			// Remove 'All' if it exists
			newSelected.delete('All');

			// Toggle the specific status
			if (newSelected.has(statusId)) {
				newSelected.delete(statusId);
			} else {
				newSelected.add(statusId);
			}

			// If no specific statuses are selected, default back to 'All'
			if (newSelected.size === 0) {
				newSelected.add('All');
			}
		}

		selectedPolicyStatuses = newSelected;

		// Clear summary card filter when dropdown filter is applied
		if (!selectedPolicyStatuses.has('All')) {
			activeFilter = null;
		}
	}

	// Load customer policy list only (fast initial load)
	async function loadPolicyList() {
		try {
			policyListLoading = true;
			loading = true;
			error = '';
			workflowStatus = 'executing';
			workflowError = '';
			workflowStep = 'Loading policy list...';
			isUsingFallback = false;

			// if (!customer?.national_id) {
			// 	throw new Error(t('policy_no_citizen_id'));
			// }

			// Fetch policy list from API (backend handles caching internally)
			workflowStep = 'Fetching policy list from API...';
			// console.log('Fetching policy list for customer:', customerId, 'platform:', platformId);

			const result = await services.customers.getCustomerPolicyList(
				customerId.toString(),
				platformId.toString(),
				access_token
			);

			if (result.res_status === 200 && result.policy_list_data) {
				workflowStep = 'Processing policy list data...';
				policyListData = result.policy_list_data;

				// console.log('Policy list workflow data received:', {
				// 	policyListData: policyListData,
				// 	customer_id: policyListData.customer_id,
				// 	execution_id: policyListData.execution_id,
				// 	policies_count: policyListData.policy_list_data.ListOfPolicyListSocial.length,
				// 	member_codes_count: policyListData.member_codes.length,
				// 	execution_time: policyListData.execution_metadata.total_execution_time_ms
				// });

				workflowStatus = 'success';
				workflowStep = 'Load policy list from API';
				// console.log('Policy list workflow executed successfully');
			} else {
				throw new Error(result.error_msg || 'Failed to load policy list from API');
			}

		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load policy list';
			workflowStatus = 'error';
			workflowError = error;
			workflowStep = 'Policy list API request failed';
			console.error('Error loading policy list:', err);
		} finally {
			policyListLoading = false;
			loading = false;
		}
	}

	// Load detailed policy information and claims for a specific member code
	// Called when user clicks on a policy from the list
	// Uses cache-first approach following the same pattern as policy list loading
	async function loadPolicyDetails(memberCode: string) {
		try {
			policyDetailsLoading = true;
			workflowStep = `Loading details for policy ${memberCode}...`;

			// if (!customerId) {
			// 	throw new Error('No customer ID provided');
			// }

			// Fetch policy details from API (backend handles caching internally)
			workflowStep = `Fetching policy details from API for ${memberCode}...`;
			// console.log('Fetching policy details for customer:', customerId, 'platform:', platformId, 'member code:', memberCode);

			const result = await services.customers.getCustomerPolicyDetails(
				customerId.toString(),
				platformId.toString(),
				memberCode,
				access_token
			);

			if (result.res_status === 200 && result.policy_details_data) {
				selectedPolicyDetails = result.policy_details_data;

				// console.log('Policy details workflow data received:', {
				// 	customer_id: selectedPolicyDetails.customer_id,
				// 	member_code: selectedPolicyDetails.member_code,
				// 	execution_id: selectedPolicyDetails.execution_id,
				// 	policy_details_count: selectedPolicyDetails.policy_details_data.ListOfPolDet.length,
				// 	claims_count: selectedPolicyDetails.policy_details_data.ListOfPolClaim.length,
				// 	execution_time: selectedPolicyDetails.execution_metadata.total_execution_time_ms
				// });

				workflowStep = 'Load policy details from API';
				// console.log('Policy details workflow executed successfully');
				toastStore.add(t('policy_details_load_success'), 'success');
			} else {
				throw new Error(result.error_msg || 'Failed to load policy details from API');
			}

		} catch (err) {
			console.error('Error loading policy details:', err);
			toastStore.add(t('policy_details_load_failed'), 'error');
		} finally {
			policyDetailsLoading = false;
		}
	}

	// Helper function to filter benefits with proper typing
	function filterBenefits(benefitString: string): string[] {
		return benefitString.split('|').filter((benefit: string) => benefit.trim());
	}

	// Handle policy card click (updated for two-stage loading)
	async function handlePolicyClick(rawPolicy: any) {
		// console.log('Policy card clicked:', {
		// 	policyNumber: rawPolicy.PolNo,
		// 	policyName: rawPolicy.PlanName,
		// 	memberCode: rawPolicy.MemberCode
		// });

		// Load detailed policy information for this member code
		await loadPolicyDetails(rawPolicy.MemberCode);

		selectedPolicy = rawPolicy;
		// console.log('Selected policy set:', selectedPolicy);

		policyDetailModalOpen = true;
		// console.log('Modal open state set to:', policyDetailModalOpen);

		// Additional check to verify state changes
		// setTimeout(() => {
		// 	console.log('Modal state after timeout:', {
		// 		policyDetailModalOpen,
		// 		selectedPolicy: selectedPolicy?.id,
		// 		modalElement: document.getElementById('policy-detail-modal-portal')
		// 	});
		// }, 100);
	}

	// Opens the policy detail modal with the selected policy
	// Sets the selected policy and opens the modal for detailed view
	// function openPolicyDetail(policy: Policy) {
	// 	selectedPolicy = policy;
	// 	policyDetailModalOpen = true;
	// }

	// Close policy detail modal
	// function closePolicyDetail() {
	// 	selectedPolicy = null;
	// 	policyDetailModalOpen = false;
	// }

	// Status color classes for badges with comprehensive status support
	type StatusKey = 'active' | 'inactive' | 'nearly_expired' | 'expired' | 'pending' | 'cancelled' | 'suspended' | 'waiting_period';
	const statusColors: Record<StatusKey, string> & Record<string, string> = {
		active: 'bg-green-100 text-green-500 border-green-200',
		pending: 'bg-blue-100 text-blue-500 border-blue-200',
		nearly_expired: 'bg-yellow-100 text-yellow-500 border-yellow-200',
		expired: 'bg-red-100 text-red-500 border-red-200',
		inactive: 'bg-gray-100 text-gray-500 border-gray-200',
		cancelled: 'bg-yellow-100 text-yellow-500 border-yellow-200',
		suspended: 'bg-orange-100 text-orange-500 border-orange-200',
		waiting_period: 'bg-yellow-100 text-yellow-600 border-yellow-200'
	};

	// Get status color class
	function getStatusColorClass(status: string): string {
		const statusKey = status.toLowerCase() as StatusKey;
		return statusColors[statusKey] || statusColors.inactive;
	}

	// Parse policy date from DD/MM/YYYY format to Date object
	function parsePolicyDate(dateString: string): Date | null {
		if (!dateString || typeof dateString !== 'string' || dateString.trim() === '') {
			return null;
		}

		const trimmed = dateString.trim();
		const parts = trimmed.split('/');

		if (parts.length !== 3) {
			return null;
		}

		const day = parseInt(parts[0], 10);
		const month = parseInt(parts[1], 10);
		const year = parseInt(parts[2], 10);

		// Validate date components
		if (isNaN(day) || isNaN(month) || isNaN(year) ||
			day < 1 || day > 31 || month < 1 || month > 12 || year < 1900) {
			return null;
		}

		// Create date object (month is 0-indexed in JavaScript)
		const date = new Date(year, month - 1, day);

		// Verify the date is valid (handles cases like 31/02/2024)
		if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
			return null;
		}

		return date;
	}

	// Format policy date for display using consistent utility
	function formatPolicyDateForDisplay(dateString: string): string {
		const parsedDate = parsePolicyDate(dateString);
		if (!parsedDate) return '-';
		
		const { date } = displayDate(parsedDate.toISOString());
		return date;
	}

	// Get display status for UI
	function getDisplayStatus(effFrom: string, effTo: string): string {
		const startDate = parsePolicyDate(effFrom);
		const endDate = parsePolicyDate(effTo);
		const currentDate = new Date();

		// Set current date to start of day for accurate comparison
		currentDate.setHours(0, 0, 0, 0);

		// If either date is invalid, return inactive
		if (!startDate || !endDate) {
			return 'inactive';
		}

		// Set dates to start of day for accurate comparison
		startDate.setHours(0, 0, 0, 0);
		endDate.setHours(0, 0, 0, 0);

		// Check if policy hasn't started yet (pending/waiting period)
		if (currentDate < startDate) {
			return 'pending';
		}

		// Check if policy has expired
		if (currentDate > endDate) {
			return 'expired';
		}

		// Check if policy is nearly expired (within 30 days)
		const thirtyDaysFromNow = new Date(currentDate);
		thirtyDaysFromNow.setDate(currentDate.getDate() + 30);

		if (thirtyDaysFromNow > endDate) {
			return 'nearly_expired';
		}

		// Policy is currently active
		return 'active';
	}

	// Initialize data loading with two-stage approach
	async function initializeData() {
		try {
			// console.log('initializeData called for customer:', customerId);
			initialDataLoading = true;

			// Load policy list first (fast initial load)
			await loadPolicyList();

			initialDataLoaded = true;
		} catch (err) {
			console.error('Failed to initialize policy list data:', err);
		} finally {
			initialDataLoading = false;
		}
	}

	// Reactive data loading when customer changes
	$: if (customerId && platformId && access_token) {
		console.log('Reactive data loading triggered for customer:', customerId, 'platform:', platformId);

		// Check if customer has changed
		const customerChanged = currentCustomerId !== null && currentCustomerId !== customerId;

		// Check if platform has changed
		const platformChanged = currentPlatformId !== null && currentPlatformId !== platformId;

		// console.log('Customer change check:', {
		// 	currentCustomerId,
		// 	newCustomerId: customerId,
		// 	customerChanged,
		// 	currentPlatformId,
		// 	newPlatformId: platformId,
		// 	platformChanged,
		// 	initialDataLoaded,
		// 	initialDataLoading
		// });

		// Only proceed if customer has actually changed or if we haven't loaded data yet
		if ((customerChanged || platformChanged) || (!initialDataLoaded && !initialDataLoading)) {
			// Reset flags if customer has changed
			if (customerChanged) {
				// console.log('Customer changed, resetting flags:', {
				// 	customerChanged,
				// 	oldCustomerId: currentCustomerId,
				// 	newCustomerId: customerId
				// });
				initialDataLoaded = false;
				initialDataLoading = false;
			}

			// Reset flags if platform has changed
			if (platformChanged) {
				// console.log('Platform changed, resetting flags:', {
				// 	platformChanged,
				// 	oldPlatformId: currentPlatformId,
				// 	newPlatformId: platformId
				// });
				initialDataLoaded = false;
				initialDataLoading = false;
			}

			// Update tracking variables
			currentCustomerId = customerId;
			currentPlatformId = platformId;

			// Only initialize if not already loaded and not loading
			if (!initialDataLoaded && !initialDataLoading) {
				// console.log('Calling initializeData from reactive statement');
				initializeData();
			}
		}
	}

	// Reactive data refresh when customer changes after initial load
	$: if (customerId && platformId && access_token && initialDataLoaded) {
		// Check if customer has changed
		const customerChanged = currentCustomerId !== null && currentCustomerId !== customerId;

		// Check if platform has changed
		const platformChanged = currentPlatformId !== null && currentPlatformId !== platformId;

		if (customerChanged || platformChanged) {
			// console.log('Customer changed after initial load, refreshing data:', {
			// 	customerChanged,
			// 	oldCustomerId: currentCustomerId,
			// 	newCustomerId: customerId,
			// 	platformChanged,
			// 	oldPlatformId: currentPlatformId,
			// 	newPlatformId: platformId
			// });

			// Customer changed - backend handles cache invalidation internally

			// Update tracking variables
			currentCustomerId = customerId;
			currentPlatformId = platformId;

			// Refresh data with cache-first approach
			loadPolicyList();
		}
	}

	// Initialize component
	onMount(() => {
		// console.log('PoliciesTab onMount triggered:', {
		// 	customer: !!customer,
		// 	customerId: customerId,
		// 	platformId: platformId,
		// 	access_token: !!access_token,
		// 	initialDataLoaded,
		// 	initialDataLoading,
		// 	currentCustomerId
		// });

		// Set the current customer ID on mount to avoid false "customer changed" detection
		if (customerId && currentCustomerId === null) {
			currentCustomerId = customerId;
		}

		// The reactive statement will handle initialization, so we don't need to do it here
		// console.log('onMount complete - reactive statement will handle initialization');
	});

	// Cleanup
	onDestroy(() => {
		// Reset initialization flags for cleanup
		initialDataLoaded = false;
		initialDataLoading = false;
	});
</script>

<div id="policies-tab-container" class="p-6" data-testid="policies-tab">
	{#if loading}
		<div class="flex gap-3 mt-8 justify-center">
			<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600"></div>
			<p class="text-sm font-medium text-gray-500">{t('policy_loading')}</p>
		</div>
	{:else if workflowStatus === 'error' && error}
		<div class="mt-8 text-center">
			<p class="mt-2 text-sm font-medium text-gray-500">{t('policy_load_failed_p1')}</p>
			<p class="mt-2 text-sm font-medium text-gray-500">{t('policy_load_failed_p2')}</p>
			<!-- {#if workflowError}
				<p class="mt-1 text-xs text-gray-400">Workflow Error: {workflowError}</p>
			{/if} -->
			<!-- <div class="mt-4 space-x-2">
				<Button on:click={loadPolicyList} color="blue">
					<RefreshOutline class="h-4 w-4 mr-1" />
					{t('try_again')}
				</Button>
			</div> -->
		</div>
	{:else if !policyListData || !policyListData.policy_list_data || policyListData.policy_list_data.ListOfPolicyListSocial.length === 0}
		<div class="mt-8 text-center">
			<h3 class="mt-2 text-sm font-medium text-gray-500">{t('policy_not_available')}</h3>
		</div>
	{:else}
		<!-- Debug -->
		<!-- Workflow Status Indicator -->
		<!-- {#if workflowStatus === 'success'}
			<div class="mb-4 rounded-lg bg-purple-50 shadow-md p-3">
				<div class="text-xs text-purple-700">
					<p>! DEBUG !</p>
					<p>Currently using these fixed value for BVTPA API calls:</p>
					<p>SOCIAL_ID: U3ef2199803607a9ec643f2461fd2f039</p>
					<p>CHANNEL_ID: 2006769099</p>
					<p>CITIZEN_ID: 2019086318637</p>
					<p>-------------------------</p>
					<p>Workflow: {workflowStep}</p>
				</div>
			</div>
		{/if} -->

		<div class="flex items-center justify-between mb-6">
			<h2 class="text-xl font-bold text-gray-900">{t('policy_tab')}</h2>
			<div class="relative">
				<Button
						id="policy-status-filter-button"
						color={activeDropdownFiltersCount > 0 ? 'dark' : 'none'}
						class="p-2 {activeDropdownFiltersCount > 0 ? '' : 'border border-gray-300 hover:bg-gray-100'} focus:ring-0"
						aria-label="Filter policy"
						data-testid="filter-policy-button"
					>
						<AdjustmentsHorizontalSolid
							id="policy-status-filter-icon"
							class="h-5 w-5"
							data-testid="filter-policy-icon"
						/>
					</Button>

					<Dropdown
						id="policy-status-filter-dropdown"
						class="w-48 p-2 shadow-lg"
						placement="bottom-end"
					>
						<div class="space-y-1">
							<Label class="flex items-center rounded px-2 py-1 text-sm">สถานะ</Label>
							{#each policyStatusOptions as option}
								<Label
									class="flex items-center rounded px-2 py-1 text-sm hover:bg-gray-50 cursor-pointer"
								>
									<Checkbox
										checked={selectedPolicyStatuses.has(option.id)}
										on:change={() => togglePolicyStatusFilter(option.id)}
										class="mr-2 text-gray-600 focus:ring-gray-500"
									/>
									<span class="text-gray-700">{t(option.label)}</span>
								</Label>
							{/each}
						</div>
					</Dropdown>
			</div>
		</div>
		<!-- <div class="font-medium text-xs text-gray-500 mb-2 ml-1">{t('policy_filter_instructions')}</div> -->
		<!-- Policy Status Summary Cards -->
		<div id="policies-tab-statistics" class="mb-4 grid grid-cols-2 md:grid-cols-4 gap-3">
			<!-- Active Policies -->
			<div
				class="flex items-end justify-between rounded-lg border-0 p-2 shadow-md
					   {getStatusColorClass('active')}
					   {selectedPolicyStatuses.has('active') ? 'ring-2 ring-green-400 shadow-lg' : ''}
					   {(!selectedPolicyStatuses.has('active') && !selectedPolicyStatuses.has('All')) ? 'opacity-50' : ''}"
			>
				<div>
					<p class="text-sm">{t('policy_status_active')}</p>
					<p class="text-xl font-semibold">{policyStatusCounts.active}</p>
				</div>
			</div>

			<!-- Pending Policies -->
			<div
				class="flex items-end justify-between rounded-lg border-0 p-2 shadow-md
					   {getStatusColorClass('pending')}
					   {selectedPolicyStatuses.has('pending') ? 'ring-2 ring-blue-400 shadow-lg' : ''}
					   {(!selectedPolicyStatuses.has('pending') && !selectedPolicyStatuses.has('All')) ? 'opacity-50' : ''}"
			>
				<div>
					<p class="text-sm">{t('policy_status_pending')}</p>
					<p class="text-xl font-semibold">{policyStatusCounts.pending}</p>
				</div>
			</div>

			<!-- Nearly Expired Policies -->
			<div
				class="flex items-end justify-between rounded-lg border-0 p-2 shadow-md
					   {getStatusColorClass('nearly_expired')}
					   {selectedPolicyStatuses.has('nearly_expired') ? 'ring-2 ring-yellow-400 shadow-lg' : ''}
					   {(!selectedPolicyStatuses.has('nearly_expired') && !selectedPolicyStatuses.has('All')) ? 'opacity-50' : ''}"
			>
				<div>
					<p class="text-sm">{t('policy_status_nearly_expired')}</p>
					<p class="text-xl font-semibold">{policyStatusCounts.nearly_expired}</p>
				</div>
			</div>

			<!-- Expired Policies -->
			<div
				class="flex items-end justify-between rounded-lg border-0 p-2 shadow-md
					   {getStatusColorClass('expired')}
					   {selectedPolicyStatuses.has('expired') ? 'ring-2 ring-red-400 shadow-lg' : ''}
					   {(!selectedPolicyStatuses.has('expired') && !selectedPolicyStatuses.has('All')) ? 'opacity-50' : ''}"
			>
				<div>
					<p class="text-sm">{t('policy_status_expired')}</p>
					<p class="text-xl font-semibold">{policyStatusCounts.expired}</p>
				</div>
			</div>
		</div>

		<!-- Policies Grid -->
		<section
			id="policies-tab-policies-grid"
			class="grid gap-3"
			style="grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));"
			aria-label="Policy cards grid"
		>
			{#if !policyListData?.policy_list_data?.ListOfPolicyListSocial || policyListData.policy_list_data.ListOfPolicyListSocial.length === 0}
				<div class="col-span-full flex flex-col items-center justify-center py-12 text-center">
					<h3 class="mb-2 text-sm font-medium text-gray-500">{t('policy_not_available')}</h3>
				</div>
			{:else if filteredPolicies.length === 0 && activeFilter}
				<div class="col-span-full flex flex-col items-center justify-center py-12 text-center">
					<h3 class="mb-2 text-sm font-medium text-gray-500">{t('policy_not_available')}</h3>
				</div>
			{:else}
				{#each filteredPolicies as rawPolicy}
					{@const displayStatus = getDisplayStatus(rawPolicy.EffFrom, rawPolicy.EffTo)}
					{@const accidentBenefits = lang === 'th'? rawPolicy.EvenMBAccidentTH : rawPolicy.EvenMBAccidentEN}
					{@const illnessBenefits = lang === 'th'? rawPolicy.EvenMBIllnessTH : rawPolicy.EvenMBIllnessEN}
					{@const otherBenefits = lang === 'th'? rawPolicy.EvenMBOtherTH : rawPolicy.EvenMBOtherEN}
					<div
						class="flex min-h-[480px] w-full
							   flex-col justify-between
							   rounded-lg bg-white
							   p-6 text-left
							   shadow-md hover:shadow-lg transition-shadow duration-200
							   {policyDetailsLoading ? 'opacity-50 cursor-wait' : 'cursor-default'}"
						role="button"
						tabindex="0"
					>
						<!-- Policy Header -->
						<div>
							<div class="mb-1 flex items-center justify-between">
								<h2 class="text-xl font-bold text-green-600 truncate w-96">
									{rawPolicy.PlanName}
								</h2>
								<Badge rounded border class="text-xs whitespace-nowrap {getStatusColorClass(displayStatus)}">
									{t('policy_status_' + displayStatus)}
								</Badge>
							</div>

							<div class="mb-4">
								<p class="text-sm text-gray-600">
									{t('policy_no')}: 
									<span class="font-medium text-gray-900">{rawPolicy.PolNo}</span>
								</p>
							</div>

							<!-- Policyholder Section -->
							<div class="mb-4">
								<div class="flex items-center mb-1">
									<!-- <UserOutline class="h-4 w-4 text-gray-600 mr-2" /> -->
									<h3 class="text-sm text-gray-900">{t('policy_holder')}</h3>
								</div>
								<div>
									<p class="text-sm text-gray-900 font-medium">{lang === 'th'? rawPolicy.Name : rawPolicy.NameEN} {lang === 'th'? rawPolicy.Surname : rawPolicy.SurnameEN}</p>
									<!-- <p class="text-sm text-gray-600">ID: {rawPolicy.CitizenID}</p> -->
								</div>
							</div>

							<!-- Insurer Section -->
							<div class="mb-4">
								<div class="flex items-center mb-1">
									<!-- <BuildingOutline class="h-4 w-4 text-gray-600 mr-2" /> -->
									<h3 class="text-sm text-gray-900">{t('policy_insurer_name')}</h3>
								</div>
								<div>
									<p class="text-sm text-gray-900 font-medium">{lang === 'th'? rawPolicy.InsurerName : rawPolicy.InsurerNameEN}</p>
								</div>
							</div>

							<!-- Coverage Period Section -->
							<div class="mb-4">
								<div class="flex items-center mb-1">
									<!-- <CalendarMonthOutline class="h-4 w-4 text-gray-600 mr-2" /> -->
									<h3 class="text-sm text-gray-900">{t('policy_effective')}</h3>
								</div>
								<div class="grid grid-cols-2 gap-4">
									<div>
										<p class="text-xs text-gray-500">{t('policy_effective_from')}</p>
										<p class="text-sm font-medium text-gray-900">
											{formatPolicyDateForDisplay(rawPolicy.EffFrom)}
										</p>
									</div>
									<div>
										<p class="text-xs text-gray-500">{t('policy_effective_to')}</p>
										<p class="text-sm font-medium text-gray-900">
											{formatPolicyDateForDisplay(rawPolicy.EffTo)}
										</p>
									</div>
								</div>
							</div>

							<!-- Coverage Benefits Section -->
							<div class="mb-6">
								<div class="flex items-center mb-1">
									<!-- <ShieldCheckOutline class="h-4 w-4 text-gray-600 mr-2" /> -->
									<h3 class="text-sm text-gray-900">{t('policy_coverage_section')}</h3>
								</div>
								<div class="space-y-2">
									{#if rawPolicy.EvenMBAccidentTH && rawPolicy.EvenMBAccidentTH.trim()}
										<div>
											<p class="text-sm font-medium text-gray-900">{t('policy_coverage_accident')}</p>
											<div class="flex flex-wrap gap-1 mt-1">
												{#each filterBenefits(accidentBenefits) as benefit}
													<Badge border color="dark" class="text-xs">
														{benefit.trim()}
													</Badge>
												{/each}
											</div>
										</div>
									{/if}
									{#if rawPolicy.EvenMBIllnessTH && rawPolicy.EvenMBIllnessTH.trim()}
										<div>
											<p class="text-sm font-medium text-gray-900">{t('policy_coverage_illness')}</p>
											<div class="flex flex-wrap gap-1 mt-1">
												{#each filterBenefits(illnessBenefits) as benefit}
													<Badge border color="dark" class="text-xs">
														{benefit.trim()}
													</Badge>
												{/each}
											</div>
										</div>
									{/if}
									{#if rawPolicy.EvenMBOtherTH && rawPolicy.EvenMBOtherTH.trim()}
										<div>
											<p class="text-sm font-medium text-gray-900">{t('policy_coverage_others')}</p>
											<div class="flex flex-wrap gap-1 mt-1">
												{#each filterBenefits(otherBenefits) as benefit}
													<Badge border color="dark" class="text-xs">
														{benefit.trim()}
													</Badge>
												{/each}
											</div>
										</div>
									{/if}
								</div>
							</div>
						</div>
						<div>
							<!-- Additional Information -->
							<div class="space-y-1 text-sm text-gray-600">
								<div class="flex justify-between">
									<span>{t('policy_member_code')}</span>
									<span class="font-medium text-gray-900">{rawPolicy.MemberCode}</span>
								</div>
								<div class="flex justify-between">
									<span>{t('policy_member_type')}</span>
									<span class="font-medium text-gray-900">{rawPolicy.CardType}</span>
								</div>
								{#if rawPolicy.CardType === 'Group Insurance' || rawPolicy.CardType === 'Self-Insured'}
									<div class="flex justify-between">
										<span>{t('policy_staff_no')}</span>
										<span class="font-medium text-gray-900">{rawPolicy.StaffNo.trim() || '-'}</span>
									</div>
								{:else if rawPolicy.CardType === 'Individual Policy'}
									<div class="flex justify-between">
										<span>{t('policy_certificate_no')}</span>
										<span class="font-medium text-gray-900">{rawPolicy.CertificateNo.trim() || '-'}</span>
									</div>
								{/if}
							</div>
							<div class="mt-4 pt-4 border-t border-gray-100">
								<Button
									on:click={() => !policyDetailsLoading && handlePolicyClick(rawPolicy)}
									color="blue"
									class="w-full {policyDetailsLoading ? 'opacity-50 cursor-wait' : 'cursor-pointer'}"
								>
									{t('policy_view_details_button')}
								</Button>
							</div>
						</div>
					</div>
				{/each}
			{/if}
		</section>
	{/if}
</div>

<!-- Policy Detail Modal with Portal -->
<ModalPortal
	bind:isOpen={policyDetailModalOpen}
	modalId="policy-detail-modal-portal"
>
	<PolicyDetailModal
		bind:isOpen={policyDetailModalOpen}
		{selectedPolicy}
		{selectedPolicyDetails}
		on:close={() => policyDetailModalOpen = false}
	/>
</ModalPortal>
